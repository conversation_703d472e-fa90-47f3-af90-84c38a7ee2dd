import 'dart:io';
import 'package:bus/bloc/add_driver_cubit/add_driver_cubit.dart';
import 'package:bus/bloc/add_driver_cubit/add_driver_states.dart';
import 'package:bus/bloc/bus_available_cubit/bus_available_cubit.dart';
import 'package:bus/bloc/bus_available_cubit/bus_available_states.dart';
import 'package:bus/bloc/driver_cubit/driver_cubit.dart';
import 'package:bus/bloc/gender_cubit/gender_cubit.dart';
import 'package:bus/bloc/update_driver_cubit/update_driver_cubit.dart';
import 'package:bus/bloc/update_driver_cubit/update_driver_states.dart';
import 'package:bus/config/global_variable.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/constant/path_route_name.dart';
import 'package:bus/data/models/bus_avaiable_models/bus_available_data_models.dart';
import 'package:bus/data/models/driver_models/driver_info_models.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/utils/assets_utils.dart';
import 'package:bus/utils/sized_box.dart';
import 'package:bus/views/custom_widgets/custom_button.dart';
import 'package:bus/views/custom_widgets/custom_drop_down_button.dart';
import 'package:bus/views/custom_widgets/custom_form_field_border.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:bus/widgets/custom_appbar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';
import 'package:easy_localization/easy_localization.dart';
// Preserve existing imports...

class AddDriverScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addDriver;
  final DriverInfoModels? driverInfo;
  final bool isEdit;

  const AddDriverScreen({super.key, required this.isEdit, this.driverInfo});

  @override
  State<AddDriverScreen> createState() => _AddDriverScreenState();
}

class _AddDriverScreenState extends State<AddDriverScreen> {
  final _formKey = GlobalKey<FormState>();

  // Form Controllers
  late final TextEditingController _phoneController;
  late final TextEditingController _usernameController;
  late final TextEditingController _nameController;
  late final TextEditingController _cityNameController;
  late final TextEditingController _addressController;
  late final TextEditingController _passwordController;
  late final TextEditingController _passwordConfirmationController;

  // Form Values
  String? _username;
  String? _name;
  int? _busId;
  String? _address;
  String? _cityName;
  String? _phone;
  File? _image;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeData();
    _loadBusAndGenderData();
  }

  void _initializeControllers() {
    _phoneController = TextEditingController();
    _usernameController = TextEditingController();
    _nameController = TextEditingController();
    _cityNameController = TextEditingController();
    _addressController = TextEditingController();
    _passwordController = TextEditingController();
    _passwordConfirmationController = TextEditingController();
  }

  void _initializeData() {
    if (widget.isEdit && widget.driverInfo != null) {
      _populateEditData();
    } else {
      _busId = 0;
    }
  }

  void _populateEditData() {
    try {
      final driverInfo = widget.driverInfo!;
      _busId = driverInfo.bus_id ?? 0;
      _name = driverInfo.name;
      _address = driverInfo.address;
      _cityName = driverInfo.city_name;
      _phone = driverInfo.phone ?? "";
      _username = driverInfo.username ?? "";

      // Set controller values
      _nameController.text = _name ?? "";
      _cityNameController.text = _cityName ?? "";
      _addressController.text = _address ?? "";
      _phoneController.text = _phone ?? "";
      _usernameController.text = _username ?? "";
    } catch (e) {
      if (kDebugMode) {
        print('Error populating edit data: $e');
      }
    }
  }

  void _loadBusAndGenderData() {
    context.read<BusAvailableCubit>().busAvailable();
    context.read<GenderCubit>().getGender();
  }

  Future<void> _pickImage() async {
    try {
      final pickedImage =
          await ImagePicker().pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        setState(() {
          _image = File(pickedImage.path);
        });
      }
    } on PlatformException catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
    }
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      titleWidget: CustomText(
        text: widget.isEdit
            ? AppStrings.editDriver.tr()
            : AppStrings.addDriver.tr(),
        fontSize: 18,
        textAlign: TextAlign.center,
        fontW: FontWeight.w600,
        color: TColor.white,
      ),
      leftWidget: _buildAppBarBackButton(),
    );
  }

  Widget _buildAppBarBackButton() {
    return context.locale.toString() == "ar"
        ? InkWell(
            onTap: () => Navigator.pop(context),
            child: SvgPicture.asset(AppAssets.arrowBack),
          )
        : InkWell(
            onTap: () => Navigator.pop(context),
            child: SvgPicture.asset(
              AppAssets.forwardArrow,
              colorFilter:
                  const ColorFilter.mode(TColor.white, BlendMode.srcIn),
              width: 25.w,
              height: 25.w,
            ),
          );
  }

  Widget _buildImagePicker() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: InkWell(
          onTap: _pickImage,
          child:
              _image != null ? _buildSelectedImage() : _buildImagePlaceholder(),
        ),
      ),
    );
  }

  Widget _buildSelectedImage() {
    return CircleAvatar(
      radius: 34.r,
      backgroundColor: TColor.white,
      child: CircleAvatar(
        backgroundImage: FileImage(_image!),
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return Stack(
      children: [
        CircleAvatar(
          radius: 35.r,
          backgroundColor: TColor.borderContainer,
          child: CircleAvatar(
            radius: 34.r,
            backgroundColor: TColor.white,
            child: Image.asset(
              assetsImages("pc.png"),
              width: 25.w,
              height: 25.w,
            ),
          ),
        ),
        _buildImagePickerIcon(),
      ],
    );
  }

  Widget _buildImagePickerIcon() {
    return Positioned(
      left: context.locale.toString() == "ar" ? 0 : null,
      right: context.locale.toString() == "ar" ? null : 0,
      bottom: 5.w,
      child: Container(
        width: 20.w,
        height: 20.w,
        decoration: const BoxDecoration(
            color: TColor.borderContainer, shape: BoxShape.circle),
        child: Icon(
          Icons.folder_copy,
          color: TColor.white,
          size: 10.sp,
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.name.tr(),
      controller: _nameController,
      formFieldWidth: 428,
      onChanged: (value) => _name = value,
      validation: AppStrings.validName.tr(),
      requierdNumber: 5,
      heightA: 53,
      hintText: AppStrings.name.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildUsernameField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.username.tr(),
      controller: _usernameController,
      formFieldWidth: 428,
      onChanged: (value) => _username = value,
      validation: AppStrings.required.tr(),
      requierdNumber: 5,
      heightA: 53,
      hintText: AppStrings.username.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildBusDropdown() {
    return BlocBuilder<BusAvailableCubit, BusAvailableStates>(
      builder: (context, state) {
        if (state is BusAvailableLoadingStates) {
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is BusAvailableSuccessStates) {
          final listOfGradeIds = _buildBusList(state);
          return CustomDropDownButton(
            items: [
              ...listOfGradeIds.map<DropdownMenuItem<int>>((value) {
                return DropdownMenuItem<int>(
                  value: value.id,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Text(value.name ?? ''),
                  ),
                );
              }),
            ],
            onChanged: (value) {
              setState(() {
                _busId = value;
              });
            },
            value: _busId,
          );
        } else {
          return Center(
            child: CustomText(
              text: AppStrings.busesNotFound.tr(),
            ),
          );
        }
      },
    );
  }

  List<BusAvailableDataModels> _buildBusList(BusAvailableSuccessStates state) {
    final Set<int> addedIds = {};
    final uniqueList = <BusAvailableDataModels>[];

    // Add edited bus if exists
    if (widget.isEdit && _busId != 0 && widget.driverInfo?.bus_id != null) {
      uniqueList.add(BusAvailableDataModels(
        id: widget.driverInfo!.bus_id!,
        name: widget.driverInfo!.bus?.name!,
      ));
      addedIds.add(widget.driverInfo!.bus_id!);
    }

    // Add "no Bus" option
    uniqueList.add(const BusAvailableDataModels(
      id: 0,
      name: "no Bus",
    ));
    addedIds.add(0);

    // Add remaining unique buses
    state.busAvailableModels?.data?.forEach((element) {
      if (element.id != null && !addedIds.contains(element.id)) {
        uniqueList.add(BusAvailableDataModels(
          id: element.id!,
          name: element.name!,
        ));
        addedIds.add(element.id!);
      }
    });

    return uniqueList;
  }

  Widget _buildAddressField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.address.tr(),
      controller: _addressController,
      formFieldWidth: 428,
      onChanged: (value) => _address = value,
      validation: AppStrings.validAddress.tr(),
      requierdNumber: 5,
      heightA: 53,
      hintText: AppStrings.address.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildPhoneField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.phoneNumber.tr(),
      controller: _phoneController,
      formFieldWidth: 428,
      onChanged: (value) => _phone = value,
      validation: AppStrings.validPhone.tr(),
      requierdNumber: 5,
      heightA: 53,
      hintText: AppStrings.phoneNumber.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
      inputType: TextInputType.number,
    );
  }

  Widget _buildPasswordField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: widget.isEdit
          ? AppStrings.newPassword.tr()
          : AppStrings.password.tr(),
      controller: _passwordController,
      formFieldWidth: 428,
      onChanged: (value) => _passwordController.text = value,
      validation: AppStrings.validPassword.tr(),
      requierdNumber: 8,
      heightA: 53,
      hintText: AppStrings.password.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildConfirmPasswordField() {
    return CustomFormFieldWithBorder(
      isTitled: true,
      title: AppStrings.confirmPassword.tr(),
      controller: _passwordConfirmationController,
      formFieldWidth: 428,
      onChanged: (value) => _passwordConfirmationController.text = value,
      checkValidatorFunc: true,
      validatorFunc: (value) {
        if (_passwordController.text != value) {
          return 'password not matching';
        }
        return null;
      },
      requierdNumber: 8,
      heightA: 53,
      hintText: AppStrings.confirmPassword.tr(),
      borderColor: TColor.fillFormFieldB,
      fillColor: TColor.fillFormFieldB,
      radiusNumber: 15.0,
      paddingRight: 37.w,
      paddingLeft: 37.w,
      contentPaddingVertical: 15,
      contentPaddingHorizontal: 15,
    );
  }

  Widget _buildUpdateButton() {
    return BlocConsumer<UpdateDriverCubit, UpdateDriverStates>(
      listener: _handleUpdateResponse,
      builder: (context, state) {
        if (state is! UpdateDriverLoadingStates) {
          return _buildActionButton(
            text: AppStrings.save.tr(),
            onTap: _handleUpdateSubmit,
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildAddButton() {
    return BlocConsumer<AddDriverCubit, AddDriverStates>(
      listener: _handleAddResponse,
      builder: (context, state) {
        if (state is! AddDriverLoadingStates) {
          return _buildActionButton(
            text: AppStrings.add.tr(),
            onTap: _handleAddSubmit,
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildActionButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.w, vertical: 30.h),
      child: CustomButton(
        text: text,
        onTap: onTap,
        width: 428,
        height: 53,
        radius: 15,
        borderColor: TColor.mainColor,
        bgColor: TColor.mainColor,
      ),
    );
  }

  void _handleUpdateResponse(BuildContext context, UpdateDriverStates state) {
    if (state is UpdateDriverSuccessStates) {
      _handleSuccess();
    } else if (state is UpdateDriverErrorStates) {
      _showErrorSnackBar(state.error ?? 'An error occurred');
    }
  }

  void _handleAddResponse(BuildContext context, AddDriverStates state) {
    if (state is AddDriverSuccessStates) {
      _handleSuccess();
    } else if (state is AddDriverErrorStates) {
      _showErrorSnackBar(state.error ?? 'An error occurred');
    }
  }

  void _handleSuccess() {
    BlocProvider.of<DriverCubit>(context).getDriver(
      pageNumber: 1,
      isFirst: true,
    );
    Navigator.pop(context);
    _showSuccessSnackBar();
  }

  void _handleUpdateSubmit() {
    context.read<UpdateDriverCubit>().updateDriver(
          name: _name,
          username: _username,
          address: _address,
          cityName: _cityName,
          phone: _phone,
          image: _image,
          busId: _busId,
          driverId: widget.driverInfo?.id,
          password: _passwordController.text,
        );
  }

  void _handleAddSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    _formKey.currentState!.save();
    context.read<AddDriverCubit>().addDriver(
          name: _name,
          username: _username,
          address: _address,
          cityName: _cityName,
          phone: _phone,
          image: _image,
          busId: _busId,
          password_confirmation: _passwordConfirmationController.text,
          password: _passwordController.text,
        );
  }

  void _showSuccessSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: TColor.greenSuccess,
        content: CustomText(
          text: widget.isEdit
              ? AppStrings.driverEdited.tr()
              : AppStrings.driverAdded.tr(),
          fontSize: 18,
          maxLine: 5,
          color: TColor.white,
        ),
      ),
    );
  }

  void _showErrorSnackBar(String error) {
    SnackBar snackBar = SnackBar(
      backgroundColor: TColor.redAccent,
      content: CustomText(
        text: error,
        fontSize: 18,
        maxLine: 5,
        color: TColor.white,
      ),
    );
    snackBarKey.currentState?.showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImagePicker(),
              const SBox(h: 40),
              _buildNameField(),
              const SBox(h: 15),
              _buildUsernameField(),
              const SBox(h: 15),
              _buildBusDropdown(),
              const SBox(h: 15),
              _buildAddressField(),
              const SBox(h: 15),
              _buildPhoneField(),
              const SBox(h: 15),
              _buildPasswordField(),
              const SBox(h: 15),
              if (!widget.isEdit) _buildConfirmPasswordField(),
              widget.isEdit ? _buildUpdateButton() : _buildAddButton(),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _usernameController.dispose();
    _nameController.dispose();
    _cityNameController.dispose();
    _addressController.dispose();
    _passwordController.dispose();
    _passwordConfirmationController.dispose();
    super.dispose();
  }
}
